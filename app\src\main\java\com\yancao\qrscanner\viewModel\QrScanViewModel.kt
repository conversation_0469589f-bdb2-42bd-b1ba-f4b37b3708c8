package com.yancao.qrscanner.viewModel

import android.app.Application
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.annotation.OptIn
import androidx.annotation.RequiresApi
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.mlkit.vision.barcode.common.Barcode
import com.yancao.qrscanner.camera.CameraManager
import com.yancao.qrscanner.domain.ScanResultsHolder
import com.yancao.qrscanner.utils.BroadCastUtils
import com.yancao.qrscanner.utils.CodeIdentify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 二维码扫描的 ViewModel
 * 负责管理摄像头操作、扫描状态和结果数据
 *
 * 功能说明：
 * - 管理摄像头的启动和停止
 * - 控制实时扫描的开启和关闭
 * - 处理拍照功能
 * - 管理扫描结果的存储和获取
 * - 提供二维码位置信息用于绘制绿框
 * - 新增：集成闪光灯和缩放控制功能
 */
@ExperimentalGetImage
class QrScanViewModel(application: Application) : AndroidViewModel(application) {

    private val cameraManager = CameraManager(application)

    // 实时扫描结果的 LiveData
    val realtimeScanResults = MutableLiveData<List<String>>()

    // 扫描状态的 LiveData
    val isScanningEnabled = MutableLiveData(false)

    // Pair的第一个元素是检测到的二维码列表，第二个元素是图像尺寸 (width, height)
    val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

    // 相机控制相关的 LiveData
    val isFlashlightEnabled = MutableLiveData(false)
    val zoomRatio = MutableLiveData(1.0f)
    val minZoomRatio = MutableLiveData(1.0f)
    val maxZoomRatio = MutableLiveData(1.0f)
    val hasFlashlight = MutableLiveData(false)

    // 曝光补偿相关状态
    private val _exposureCompensation = MutableLiveData<Int>()
    val exposureCompensation: LiveData<Int> = _exposureCompensation

    private val _minExposureCompensation = MutableLiveData<Int>()
    val minExposureCompensation: LiveData<Int> = _minExposureCompensation

    private val _maxExposureCompensation = MutableLiveData<Int>()
    val maxExposureCompensation: LiveData<Int> = _maxExposureCompensation

    private val _exposureCompensationStep = MutableLiveData<Float>()

    private val lastBroadcast = mutableSetOf<String>()
    private var lastBroadcastTime = 0L
    private val DEBOUNCE_DELAY = 1000L // 1秒内不重复广播相同内容

    //二维码核验相关
    // 添加CodeIdentify相关属性
    private var codeIdentify: CodeIdentify? = null
    private val _codeIdentifyStatus = MutableLiveData<String>()
    val codeIdentifyStatus: LiveData<String> = _codeIdentifyStatus
    val qrCodeVerificationResults = MutableLiveData<Map<String, Int>>()


    /**
     * 启动相机
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     * @param enableRealtimeScanning 是否启用实时扫描，默认为 false
     */
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false
    ) {
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = enableRealtimeScanning,
            onQRCodeDetected = { detectedCodes ->
                // 实时扫描回调，更新 LiveData
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 位置信息回调，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 新增：初始化相机控制相关状态
        initializeCameraControlStates()
    }

    /**
     * 初始化相机控制状态
     */
    private fun initializeCameraControlStates() {
        // 检查闪光灯支持
        hasFlashlight.postValue(cameraManager.hasFlashlight())

        // 初始化缩放范围
        minZoomRatio.postValue(cameraManager.getMinZoomRatio())
        maxZoomRatio.postValue(cameraManager.getMaxZoomRatio())
        zoomRatio.postValue(cameraManager.getCurrentZoomRatio())

        // 初始化曝光补偿范围
        _minExposureCompensation.postValue(cameraManager.getMinExposureCompensation())
        _maxExposureCompensation.postValue(cameraManager.getMaxExposureCompensation())
        _exposureCompensation.postValue(cameraManager.getCurrentExposureCompensation())
        _exposureCompensationStep.postValue(cameraManager.getExposureCompensationStep())
    }

    /**
     * 开始实时扫描
     * 这个方法会启用摄像头的实时二维码扫描功能
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(true)

        // 重新启动摄像头，这次启用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = true,
            onQRCodeDetected = { detectedCodes ->
                // 更新扫描结果
                realtimeScanResults.postValue(detectedCodes)
                realTimeBroadcast(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 立即更新位置信息，先显示绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 停止实时扫描
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun stopRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(false)

        // 重新启动摄像头，这次禁用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = false,
            onQRCodeDetected = null,
            onQRCodeWithPosition = null
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 切换闪光灯状态
     * @return 操作是否成功
     */
    fun toggleFlashlight(): Boolean {
        val currentState = isFlashlightEnabled.value ?: false
        val newState = !currentState

        return if (cameraManager.setFlashlight(newState)) {
            isFlashlightEnabled.postValue(newState)
            true
        } else {
            false
        }
    }

    /**
     * 实时广播（带代码验证）
     * @param data 检测到的二维码结果组
     */
    private fun realTimeBroadcast(data: List<String>) {

        /**
         * 检验和广播并行，先广播，再检验，若不通过，则该二维码只广播一次，其他二维码多次广播。
         * 二维码只检验一次。
         */
        val currentTime = System.currentTimeMillis()
        val verificationResults = mutableMapOf<String, Int>()
        // 过滤出需要广播的二维码（去重 + 防抖）
        val toBroadcast = data.filter { code ->
            code !in lastBroadcast
        }
        // 只处理需要广播的二维码，避免重复处理
        toBroadcast.forEach { result ->
            if (result.isNotEmpty()){
                lastBroadcast.add(result)
            }
            // 特殊长度的二维码直接发送，不需要网络核验
            if (result.length == 105) {
                sendBroadcast(result)
                return@forEach
            }

            // 其他二维码需要网络核验
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    val verificationResult = codeIdentify?.codeClassfy(result) ?: 0
                    verificationResults[result] = verificationResult
                    // 只有验证通过（返回0）才发送广播
                    if (verificationResult == 0 || verificationResult == 2) {
                        // 在主线程发送广播
                        withContext(Dispatchers.Main) {
                            sendBroadcast(result)
                        }
                    } else {
                        // 验证失败，通知UI
                        val statusMessage = when (verificationResult) {
                            1 -> "检测到未关联的盒条码: $result"
                            2 -> "检测到已关联的条码: $result"
                            else -> "代码验证异常: $result"
                        }
                        _codeIdentifyStatus.postValue(statusMessage)
                    }
                } catch (e: Exception) {
                    _codeIdentifyStatus.postValue("代码验证失败: ${e.message}")
                }
                qrCodeVerificationResults.postValue(verificationResults)
            }
        }

        // 更新防抖时间戳
        if (toBroadcast.isNotEmpty()) {
            lastBroadcastTime = currentTime
            // 清理过大的缓存
            if (lastBroadcast.size > 100) {
                lastBroadcast.clear()
            }
        }
    }

    /**
     * 发送广播的私有方法
     */
    private fun sendBroadcast(result: String) {
        val broadcastIntent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
            putExtra(BroadCastUtils.BROADCAST_RAW_DATA, result)
        }
        getApplication<Application>().sendBroadcast(broadcastIntent)
    }


    /**
     * 设置缩放比例
     * @param ratio 缩放比例
     * @return 操作是否成功
     */
    fun setZoomRatio(ratio: Float): Boolean {
        return if (cameraManager.setZoomRatio(ratio)) {
            zoomRatio.postValue(ratio)
            true
        } else {
            false
        }
    }


    /**
     * 获取当前存储的所有扫描结果
     * @return 扫描结果列表
     */
    fun getAllScanResults(): List<String> {
        return ScanResultsHolder.scanResults
    }

    /**
     * 设置曝光补偿
     * @param index 曝光补偿索引
     * @return 操作是否成功
     */
    fun setExposureCompensation(index: Int): Boolean {
        return if (cameraManager.setExposureCompensation(index)) {
            _exposureCompensation.postValue(index)
            true
        } else {
            false
        }
    }

    // 初始化CodeIdentify
    @RequiresApi(Build.VERSION_CODES.O)
    fun initializeCodeIdentify() {
        codeIdentify = CodeIdentify(
            loginUsername = "61541",
            loginPassword = "Jbcj@1#2",
            statusCallback = { status ->
                _codeIdentifyStatus.postValue(status)
            }
        )

        // 在后台线程获取token
        viewModelScope.launch(Dispatchers.IO) {
            try {
                codeIdentify?.tokenGetter()
            } catch (e: Exception) {
                _codeIdentifyStatus.postValue("初始化失败: ${e.message}")
            }
        }
    }

    /**
     * ViewModel 清理时的操作
     * 释放摄像头资源，防止内存泄漏
     */
    @OptIn(ExperimentalGetImage::class)
    override fun onCleared() {
        super.onCleared()
        cameraManager.shutdown()
    }
}