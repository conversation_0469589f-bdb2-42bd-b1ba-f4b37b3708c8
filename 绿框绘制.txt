我来为你提供在预览图中绘制绿框标识已扫描二维码的解决方案。

## 实现思路

1. **创建自定义覆盖层View**：在PreviewView上方添加一个透明的绘制层
2. **实时检测二维码位置**：在扫描过程中获取二维码的坐标信息
3. **绘制绿框标识**：在检测到的二维码位置绘制绿色边框
4. **控制绘制频率**：通过帧计数器控制绘制频率

## 1. 创建自定义绘制覆盖层

**新文件：** `app/src/main/java/com/yancao/qrscanner/ui/QRCodeOverlayView.kt`

```kotlin
package com.yancao.qrscanner.ui

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.google.mlkit.vision.barcode.common.Barcode

/**
 * 二维码覆盖层视图
 * 用于在摄像头预览上绘制已扫描二维码的绿色边框
 * 
 * 设计说明：
 * - 继承View，实现自定义绘制
 * - 维护已扫描二维码的位置信息
 * - 支持控制绘制频率，避免过度绘制
 * - 自动清理过期的绘制信息
 */
class QRCodeOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关的画笔和样式
    private val scannedQRPaint = Paint().apply {
        color = Color.GREEN
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
    }
    
    private val textPaint = Paint().apply {
        color = Color.GREEN
        textSize = 32f
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }

    // 存储已扫描二维码的信息
    private data class ScannedQRInfo(
        val boundingBox: Rect,
        val content: String,
        val timestamp: Long,
        val drawCount: Int = 0
    )

    // 已扫描的二维码列表
    private val scannedQRCodes = mutableListOf<ScannedQRInfo>()
    
    // 绘制控制变量
    private var frameCounter = 0
    
    /**
     * 控制绘制频率的变量
     * 值越大，绘制频率越低（例如：5表示每5帧绘制一次）
     * 可以通过 setDrawFrequency() 方法修改
     */
    private var drawFrequency = 3 // 默认每3帧绘制一次
    
    // 二维码显示持续时间（毫秒）
    private val qrDisplayDuration = 3000L // 3秒后自动清除
    
    // 最大显示的二维码数量
    private val maxDisplayQRCount = 10

    /**
     * 设置绘制频率
     * @param frequency 绘制频率，值越大绘制越不频繁
     */
    fun setDrawFrequency(frequency: Int) {
        drawFrequency = if (frequency > 0) frequency else 1
    }

    /**
     * 添加已扫描的二维码信息
     * @param barcode MLKit检测到的二维码对象
     * @param previewWidth 预览视图的宽度
     * @param previewHeight 预览视图的高度
     * @param imageWidth 图像的实际宽度
     * @param imageHeight 图像的实际高度
     */
    fun addScannedQRCode(
        barcode: Barcode,
        previewWidth: Int,
        previewHeight: Int,
        imageWidth: Int,
        imageHeight: Int
    ) {
        barcode.boundingBox?.let { boundingBox ->
            // 将图像坐标转换为预览视图坐标
            val scaledBoundingBox = scaleRect(
                boundingBox,
                imageWidth,
                imageHeight,
                previewWidth,
                previewHeight
            )
            
            val qrContent = barcode.rawValue ?: ""
            
            // 检查是否已经存在相同内容的二维码
            val existingIndex = scannedQRCodes.indexOfFirst { it.content == qrContent }
            
            if (existingIndex != -1) {
                // 更新现有二维码的位置和时间戳
                scannedQRCodes[existingIndex] = scannedQRCodes[existingIndex].copy(
                    boundingBox = scaledBoundingBox,
                    timestamp = System.currentTimeMillis()
                )
            } else {
                // 添加新的二维码信息
                val newQRInfo = ScannedQRInfo(
                    boundingBox = scaledBoundingBox,
                    content = qrContent,
                    timestamp = System.currentTimeMillis()
                )
                
                scannedQRCodes.add(newQRInfo)
                
                // 限制显示的二维码数量
                if (scannedQRCodes.size > maxDisplayQRCount) {
                    scannedQRCodes.removeAt(0) // 移除最旧的
                }
            }
            
            // 触发重绘
            invalidate()
        }
    }

    /**
     * 清除所有已扫描的二维码标识
     */
    fun clearScannedQRCodes() {
        scannedQRCodes.clear()
        invalidate()
    }

    /**
     * 清除过期的二维码标识
     */
    private fun clearExpiredQRCodes() {
        val currentTime = System.currentTimeMillis()
        scannedQRCodes.removeAll { currentTime - it.timestamp > qrDisplayDuration }
    }

    /**
     * 将图像坐标系的矩形转换为预览视图坐标系
     */
    private fun scaleRect(
        rect: Rect,
        imageWidth: Int,
        imageHeight: Int,
        previewWidth: Int,
        previewHeight: Int
    ): Rect {
        val scaleX = previewWidth.toFloat() / imageWidth.toFloat()
        val scaleY = previewHeight.toFloat() / imageHeight.toFloat()
        
        return Rect(
            (rect.left * scaleX).toInt(),
            (rect.top * scaleY).toInt(),
            (rect.right * scaleX).toInt(),
            (rect.bottom * scaleY).toInt()
        )
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 控制绘制频率
        frameCounter++
        if (frameCounter % drawFrequency != 0) {
            return
        }
        
        // 清除过期的二维码
        clearExpiredQRCodes()
        
        // 绘制所有已扫描的二维码边框
        scannedQRCodes.forEach { qrInfo ->
            drawQRCodeFrame(canvas, qrInfo)
        }
    }

    /**
     * 绘制单个二维码的边框和标识
     */
    private fun drawQRCodeFrame(canvas: Canvas, qrInfo: ScannedQRInfo) {
        val rect = qrInfo.boundingBox
        
        // 绘制绿色边框
        canvas.drawRect(rect, scannedQRPaint)
        
        // 绘制四个角的加强标识
        val cornerLength = 30f
        val cornerStroke = 12f
        
        val cornerPaint = Paint().apply {
            color = Color.GREEN
            style = Paint.Style.STROKE
            strokeWidth = cornerStroke
            isAntiAlias = true
        }
        
        // 左上角
        canvas.drawLine(rect.left.toFloat(), rect.top.toFloat(), 
                       rect.left + cornerLength, rect.top.toFloat(), cornerPaint)
        canvas.drawLine(rect.left.toFloat(), rect.top.toFloat(), 
                       rect.left.toFloat(), rect.top + cornerLength, cornerPaint)
        
        // 右上角
        canvas.drawLine(rect.right.toFloat(), rect.top.toFloat(), 
                       rect.right - cornerLength, rect.top.toFloat(), cornerPaint)
        canvas.drawLine(rect.right.toFloat(), rect.top.toFloat(), 
                       rect.right.toFloat(), rect.top + cornerLength, cornerPaint)
        
        // 左下角
        canvas.drawLine(rect.left.toFloat(), rect.bottom.toFloat(), 
                       rect.left + cornerLength, rect.bottom.toFloat(), cornerPaint)
        canvas.drawLine(rect.left.toFloat(), rect.bottom.toFloat(), 
                       rect.left.toFloat(), rect.bottom - cornerLength, cornerPaint)
        
        // 右下角
        canvas.drawLine(rect.right.toFloat(), rect.bottom.toFloat(), 
                       rect.right - cornerLength, rect.bottom.toFloat(), cornerPaint)
        canvas.drawLine(rect.right.toFloat(), rect.bottom.toFloat(), 
                       rect.right.toFloat(), rect.bottom - cornerLength, cornerPaint)
        
        // 在二维码上方绘制"已扫描"标识
        val text = "✓ 已扫描"
        val textX = rect.left.toFloat()
        val textY = rect.top.toFloat() - 10f
        
        // 绘制文字背景
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        val backgroundRect = RectF(
            textX - 5f,
            textY - textBounds.height() - 5f,
            textX + textBounds.width() + 5f,
            textY + 5f
        )
        
        val backgroundPaint = Paint().apply {
            color = Color.BLACK
            alpha = 180
        }
        canvas.drawRoundRect(backgroundRect, 8f, 8f, backgroundPaint)
        
        // 绘制文字
        canvas.drawText(text, textX, textY, textPaint)
    }
}
```

## 2. 修改布局文件

**修改文件：** `app/src/main/res/layout/activity_main.xml`

在现有布局基础上添加覆盖层：

```xml
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.QrScanActivity">

    <!-- 摄像头预览组件 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 二维码覆盖层 - 新增 -->
    <com.yancao.qrscanner.ui.QRCodeOverlayView
        android:id="@+id/qr_overlay_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/preview_view"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintStart_toStartOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view" />

    <!-- 按钮容器，用于放置两个按钮 -->
    <LinearLayout
        android:id="@+id/button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 扫描按钮（原来的拍照按钮，改名为扫描） -->
        <Button
            android:id="@+id/btn_scan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="扫描"
            android:background="@android:color/holo_blue_light"
            android:textColor="@android:color/white" />

        <!-- 新增的广播按钮 -->
        <Button
            android:id="@+id/btn_broadcast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="广播结果"
            android:background="@android:color/holo_green_light"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <!-- 扫描结果显示区域 -->
    <TextView
        android:id="@+id/tv_scan_results"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:background="@android:color/black"
        android:textColor="@android:color/white"
        android:padding="8dp"
        android:text="扫描结果将显示在这里..."
        android:textSize="12sp"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@+id/button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
```

## 3. 修改实时二维码分析器

**修改文件：** `app/src/main/java/com/yancao/qrscanner/camera/RealtimeQRAnalyzer.kt`

在现有代码基础上添加位置信息回调：

```kotlin
package com.yancao.qrscanner.camera

import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import com.yancao.qrscanner.utils.ScanResultsHolder

/**
 * 实时二维码分析器
 * 这个类用于实时分析摄像头预览中的二维码，并将结果存储到全局结果数组中
 * 
 * 设计说明：
 * - 继承 ImageAnalysis.Analyzer 接口，用于 CameraX 的图像分析
 * - 使用 MLKit 进行二维码识别
 * - 自动将识别结果存储到 ScanResultsHolder 中
 * - 支持回调函数，通知外部识别结果和位置信息
 * 
 * @ExperimentalGetImage 注解说明：
 * - 这个注解表明我们使用了 CameraX 的实验性 API (imageProxy.image)
 * - 虽然是实验性的，但这个 API 已经相对稳定，广泛用于图像分析场景
 * - 在未来的 CameraX 版本中，这个 API 可能会变为正式 API
 */
@ExperimentalGetImage
class RealtimeQRAnalyzer(
    private val onQRCodeDetected: ((List<String>) -> Unit)? = null,
    // 新增：二维码位置信息回调
    private val onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
) : ImageAnalysis.Analyzer {

    // MLKit 二维码扫描器配置
    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(Barcode.FORMAT_QR_CODE) // 只识别二维码
        .build()

    private val scanner = BarcodeScanning.getClient(options)
    
    // 用于控制扫描频率，避免过于频繁的扫描
    private var lastAnalyzedTimestamp = 0L
    private val analyzeInterval = 1000L // 1秒扫描一次

    /**
     * 分析图像的核心方法
     * 这个方法会被 CameraX 自动调用，传入摄像头的每一帧图像
     */
    override fun analyze(imageProxy: ImageProxy) {
        val currentTimestamp = System.currentTimeMillis()
        
        // 控制扫描频率，避免过于频繁
        if (currentTimestamp - lastAnalyzedTimestamp < analyzeInterval) {
            imageProxy.close()
            return
        }
        
        lastAnalyzedTimestamp = currentTimestamp

        // 将 ImageProxy 转换为 MLKit 可以处理的 InputImage
        // 注意：imageProxy.image 是实验性 API，需要 @ExperimentalGetImage 注解
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage, 
                imageProxy.imageInfo.rotationDegrees
            )

            // 获取图像尺寸信息
            val imageWidth = mediaImage.width
            val imageHeight = mediaImage.height

            // 使用 MLKit 扫描二维码
            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    // 扫描成功，处理结果
                    if (barcodes.isNotEmpty()) {
                        val results = barcodes.mapNotNull { it.rawValue }
                        
                        // 将结果存储到全局数组中
                        ScanResultsHolder.addScanResults(results)
                        
                        // 通知外部有新的扫描结果（原有回调）
                        onQRCodeDetected?.invoke(results)
                        
                        // 新增：通知外部二维码位置信息
                        onQRCodeWithPosition?.invoke(barcodes, imageWidth, imageHeight)
                    }
                }
                .addOnFailureListener { exception ->
                    // 扫描失败，记录错误（在实际应用中可以添加日志）
                    println("实时二维码扫描失败: ${exception.message}")
                }
                .addOnCompleteListener {
                    // 无论成功还是失败，都要关闭 ImageProxy 释放资源
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }
}
```

## 4. 修改 CameraManager

**修改文件：** `app/src/main/java/com/yancao/qrscanner/camera/CameraManager.kt`

在现有代码基础上添加位置信息回调支持：

```kotlin
// 在现有的 startCamera 方法中添加新的参数
@ExperimentalGetImage
fun startCamera(
    previewView: PreviewView, 
    lifecycleOwner: LifecycleOwner,
    enableRealtimeScanning: Boolean = false,
    onQRCodeDetected: ((List<String>) -> Unit)? = null,
    // 新增：二维码位置信息回调
    onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
) {
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

    cameraProviderFuture.addListener({
        cameraProvider = cameraProviderFuture.get()

        val preview = Preview.Builder()
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
            .build()

        // 新增：如果启用实时扫描，创建图像分析器
        val useCases = mutableListOf<androidx.camera.core.UseCase>(preview, imageCapture)
        
        if (enableRealtimeScanning) {
            // 修改：传递位置信息回调
            qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
            imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build()
                .also {
                    it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                }
            useCases.add(imageAnalyzer!!)
        }

        try {
            cameraProvider?.unbindAll()
            cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                CameraSelector.DEFAULT_BACK_CAMERA,
                *useCases.toTypedArray() // 绑定所有用例
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }, ContextCompat.getMainExecutor(context))
}
```

## 5. 修改 QrScanViewModel

**修改文件：** `app/src/main/java/com/yancao/qrscanner/viewModel/QrScanViewModel.kt`

在现有代码基础上添加位置信息支持：

```kotlin
// 在现有代码基础上添加新的 LiveData
val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

// 修改 startRealtimeScanning 方法
@ExperimentalGetImage
fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
    isScanningEnabled.postValue(true)
    // 重新启动摄像头，这次启用实时扫描
    cameraManager.startCamera(
        previewView, 
        lifecycleOwner, 
        enableRealtimeScanning = true,
        onQRCodeDetected = { detectedCodes ->
            realtimeScanResults.postValue(detectedCodes)
        },
        // 新增：位置信息回调
        onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
            qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
        }
    )
}
```

## 6. 修改 QrScanActivity

**修改文件：** `app/src/main/java/com/yancao/qrscanner/ui/QrScanActivity.kt`

在现有代码基础上添加覆盖层控制：

```kotlin
// 在 onCreate 方法中添加覆盖层相关代码

override fun onCreate(savedInstanceState: Bundle?) {
    // ... 现有代码保持不变 ...

    // 新增：设置覆盖层绘制频率（可以根据需要调整）
    binding.qrOverlayView.setDrawFrequency(5) // 每5帧绘制一次

    // 新增：观察二维码位置信息
    viewModel.qrCodePositions.observe(this) { (barcodes, imageSizes) ->
        val (imageWidth, imageHeight) = imageSizes
        val previewWidth = binding.previewView.width
        val previewHeight = binding.previewView.height
        
        // 为每个检测到的二维码添加绿框标识
        barcodes.forEach { barcode ->
            binding.qrOverlayView.addScannedQRCode(
                barcode,
                previewWidth,
                previewHeight,
                imageWidth,
                imageHeight
            )
        }
    }

    // 修改扫描状态观察，在停止扫描时清除覆盖层
    viewModel.isScanningEnabled.observe(this) { isScanning ->
        updateScanButtonText(isScanning)
        
        // 新增：停止扫描时清除绿框
        if (!isScanning) {
            binding.qrOverlayView.clearScannedQRCodes()
        }
    }

    // ... 其他现有代码保持不变 ...
}
```

## 使用说明

### 控制绘制频率
你可以通过以下方式控制绘制频率：

```kotlin
// 在 QrScanActivity 中调整绘制频率
binding.qrOverlayView.setDrawFrequency(3) // 每3帧绘制一次
binding.qrOverlayView.setDrawFrequency(5) // 每5帧绘制一次
binding.qrOverlayView.setDrawFrequency(10) // 每10帧绘制一次
```

### 自定义样式
你可以在 `QRCodeOverlayView` 中修改以下变量来自定义样式：

- `scannedQRPaint.color`：边框颜色
- `scannedQRPaint.strokeWidth`：边框粗细
- `qrDisplayDuration`：绿框显示持续时间
- `maxDisplayQRCount`：最大同时显示的二维码数量

### 性能优化
- 通过 `drawFrequency` 控制绘制频率，避免过度绘制
- 自动清理过期的二维码标识，避免内存泄漏
- 限制同时显示的二维码数量，保持界面清晰

这个方案实现了你的需求：
1. 在扫描到二维码后绘制绿框标识
2. 提供了绘制频率控制变量
3. 自动管理绿框的显示和清理
4. 性能优化，避免过度绘制
