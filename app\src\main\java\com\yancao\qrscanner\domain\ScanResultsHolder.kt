package com.yancao.qrscanner.domain

/**
 * 全局二维码扫描结果存储类
 * 这个类用于存储实时扫描的二维码结果，供其他模块访问
 *
 * 设计说明：
 * - 使用 object 关键字创建单例对象，确保全局只有一个实例
 * - 使用 MutableList 存储扫描结果，支持动态添加和删除
 * - 提供线程安全的操作方法，防止多线程访问时出现问题
 */
object ScanResultsHolder {

    // 存储扫描结果的数组（结果A）
    // @Volatile 注解确保多线程环境下的可见性
    @Volatile
    private var _scanResults: MutableList<String> = mutableListOf()

    // 只读的扫描结果列表，外部只能读取不能直接修改
    val scanResults: List<String>
        get() = _scanResults.toList() // 返回副本，防止外部直接修改

    /**
     * 添加新的扫描结果
     * @param result 扫描到的二维码内容
     */
    @Synchronized
    fun addScanResult(result: String) {
        if (result.isNotBlank() && !_scanResults.contains(result)) {
            _scanResults.add(result)
            println("添加扫描结果: $result，当前总数: ${_scanResults.size}")
        }
    }

    /**
     * 批量添加扫描结果
     * @param results 扫描结果列表
     */
    @Synchronized
    fun addScanResults(results: List<String>) {
        results.forEach { result ->
            if (result.isNotBlank() && !_scanResults.contains(result)) {
                _scanResults.add(result)
            }
        }
        println("批量添加扫描结果，当前总数: ${_scanResults.size}")
    }

    /**
     * 清空所有扫描结果
     */
    @Synchronized
    fun clearResults() {
        _scanResults.clear()
        println("已清空所有扫描结果")
    }

    /**
     * 获取扫描结果数量
     */
    fun getResultCount(): Int = _scanResults.size

    /**
     * 检查是否有扫描结果
     */
    fun hasResults(): Boolean = _scanResults.isNotEmpty()

    /**
     * 获取最新的扫描结果
     */
    fun getLatestResult(): String? = _scanResults.lastOrNull()
}