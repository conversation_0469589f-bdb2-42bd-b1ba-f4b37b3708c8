package com.yancao.qrscanner.camera

import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import com.yancao.qrscanner.domain.ScanResultsHolder
import com.yancao.qrscanner.domain.ScanSettingsManager

/**
 * 实时二维码分析器
 * 支持动态扫描频率控制
 */
@ExperimentalGetImage
class RealtimeQRAnalyzer(
    private val onQRCodeDetected: ((List<String>) -> Unit)? = null,
    private val onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
) : ImageAnalysis.Analyzer {

    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
        .build()

    private val scanner = BarcodeScanning.getClient(options)

    // 用于控制扫描频率
    private var lastAnalyzedTimestamp = 0L

    /**
     * 获取当前扫描间隔
     */
    private fun getCurrentAnalyzeInterval(): Long {
        return ScanSettingsManager.getScanIntervalMs()
    }

    override fun analyze(imageProxy: ImageProxy) {
        val currentTimestamp = System.currentTimeMillis()

        // 使用动态扫描频率
        if (currentTimestamp - lastAnalyzedTimestamp < getCurrentAnalyzeInterval()) {
            imageProxy.close()
            return
        }

        lastAnalyzedTimestamp = currentTimestamp

        val mediaImage = imageProxy.image

        if (mediaImage != null) {
            val rotationDegrees = imageProxy.imageInfo.rotationDegrees

            val imageWidth: Int
            val imageHeight: Int

            when (rotationDegrees) {
                0, 180 -> {
                    imageWidth = mediaImage.width
                    imageHeight = mediaImage.height
                }
                90, 270 -> {
                    imageWidth = mediaImage.height
                    imageHeight = mediaImage.width
                }
                else -> {
                    imageWidth = mediaImage.width
                    imageHeight = mediaImage.height
                }
            }

            val image = InputImage.fromMediaImage(
                mediaImage,
                rotationDegrees
            )

            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    if (barcodes.isNotEmpty()) {
                        val results = barcodes.mapNotNull { it.rawValue }
                        ScanResultsHolder.addScanResults(results)
                        onQRCodeDetected?.invoke(results)
                        onQRCodeWithPosition?.invoke(barcodes, imageWidth, imageHeight)
                    } else {
                        onQRCodeWithPosition?.invoke(emptyList(), imageWidth, imageHeight)
                    }
                }
                .addOnFailureListener { exception ->
                    println("实时二维码扫描失败: ${exception.message}")
                    onQRCodeWithPosition?.invoke(emptyList(), imageWidth, imageHeight)
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }
}