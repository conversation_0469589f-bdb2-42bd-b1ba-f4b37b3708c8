package com.yancao.qrscanner.utils

import android.app.Activity
import android.graphics.Point
import android.os.Build
import android.util.DisplayMetrics

class DisplayUtils {
    companion object{
        /**
         * 获取应用程序的可用显示区域尺寸（已去除状态栏和导航栏）。
         * 这是获取内容可以实际绘制区域大小的最推荐方法。
         *
         * @param activity 当前的 Activity。
         * @return 一个包含可用宽度和高度的 Point 对象（单位：像素）。
         */
        fun getAppUsableScreenSize(activity: Activity): Point {
            val size = Point()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // 对于 Android 11 (API 30) 及以上版本
                val windowMetrics = activity.windowManager.currentWindowMetrics
                val bounds = windowMetrics.bounds
                size.x = bounds.width()
                size.y = bounds.height()
            } else {
                // 对于旧版本
                @Suppress("DEPRECATION")
                val display = activity.windowManager.defaultDisplay
                val displayMetrics = DisplayMetrics()
                @Suppress("DEPRECATION")
                display.getMetrics(displayMetrics)
                size.x = displayMetrics.widthPixels
                size.y = displayMetrics.heightPixels
            }
            return size
        }
    }
}