package com.yancao.qrscanner.utils

import android.graphics.Bitmap
import kotlin.math.min
import androidx.core.graphics.scale

object BitmapSizeReduce {
    fun resizeBitmap(source: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        if (source.width <= maxWidth && source.height <= maxHeight) {
            return source
        }

        val originalWidth = source.width
        val originalHeight = source.height

        val ratio = min(
            maxWidth.toFloat() / originalWidth,
            maxHeight.toFloat() / originalHeight
        )

        // 如果计算出的比例大于等于1，说明无需缩小，直接返回原图
        if (ratio >= 1.0f) {
            return source
        }

        val newWidth = (originalWidth * ratio).toInt()
        val newHeight = (originalHeight * ratio).toInt()

        return source.scale(newWidth, newHeight)
    }
}