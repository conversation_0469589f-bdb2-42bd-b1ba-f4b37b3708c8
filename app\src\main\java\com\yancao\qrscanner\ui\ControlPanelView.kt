package com.yancao.qrscanner.ui

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ArrayAdapter
import android.widget.FrameLayout
import com.yancao.qrscanner.R
import com.yancao.qrscanner.databinding.ControlPanelOverlayBinding
import com.yancao.qrscanner.domain.FrequencyUnit
import com.yancao.qrscanner.domain.ScanSettings
import com.yancao.qrscanner.domain.ScanSettingsManager

/**
 * 扫描控制面板组件
 */
class ControlPanelView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding: ControlPanelOverlayBinding
    private var onSettingsApplied: ((ScanSettings) -> Unit)? = null
    private var onPanelClosed: (() -> Unit)? = null

    init {
        binding = ControlPanelOverlayBinding.inflate(LayoutInflater.from(context), this, true)
        setupSpinners()
        setupClickListeners()
        loadCurrentSettings()
    }

    /**
     * 设置回调函数
     */
    fun setCallbacks(
        onSettingsApplied: (ScanSettings) -> Unit,
        onPanelClosed: () -> Unit
    ) {
        this.onSettingsApplied = onSettingsApplied
        this.onPanelClosed = onPanelClosed
    }

    /**
     * 设置下拉框
     */
    private fun setupSpinners() {
        val units = FrequencyUnit.values().map { it.displayName }

        // 扫描频率单位下拉框
        val scanUnitAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, units)
        scanUnitAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerScanUnit.adapter = scanUnitAdapter

        // 绘制频率单位下拉框
        val drawUnitAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, units)
        drawUnitAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerDrawUnit.adapter = drawUnitAdapter
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.btnApplySettings.setOnClickListener {
            applySettings()
        }

        binding.btnCancelSettings.setOnClickListener {
            onPanelClosed?.invoke()
        }
    }

    /**
     * 加载当前设置
     */
    private fun loadCurrentSettings() {
        val settings = ScanSettingsManager.getCurrentSettings()

        binding.etScanFrequency.setText(settings.scanFrequencyValue.toString())
        binding.spinnerScanUnit.setSelection(settings.scanFrequencyUnit.ordinal)

        binding.etDrawFrequency.setText(settings.drawFrequencyValue.toString())
        binding.spinnerDrawUnit.setSelection(settings.drawFrequencyUnit.ordinal)
    }

    /**
     * 应用设置
     */
    private fun applySettings() {
        try {
            val scanFrequencyValue = binding.etScanFrequency.text.toString().toIntOrNull() ?: 500
            val scanFrequencyUnit = FrequencyUnit.values()[binding.spinnerScanUnit.selectedItemPosition]

            val drawFrequencyValue = binding.etDrawFrequency.text.toString().toIntOrNull() ?: 5
            val drawFrequencyUnit = FrequencyUnit.values()[binding.spinnerDrawUnit.selectedItemPosition]

            val newSettings = ScanSettings(
                scanFrequencyValue = scanFrequencyValue.coerceAtLeast(1),
                scanFrequencyUnit = scanFrequencyUnit,
                drawFrequencyValue = drawFrequencyValue.coerceAtLeast(1),
                drawFrequencyUnit = drawFrequencyUnit
            )

            ScanSettingsManager.updateSettings(newSettings)
            onSettingsApplied?.invoke(newSettings)
            onPanelClosed?.invoke()
        } catch (e: Exception) {
            // 处理输入错误
            android.widget.Toast.makeText(context, "输入格式错误，请检查数值", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 显示面板
     */
    fun show() {
        loadCurrentSettings()
        visibility = View.VISIBLE
    }

    /**
     * 隐藏面板
     */
    fun hide() {
        visibility = View.GONE
    }
}