这是一个很常见的问题。您遇到的情况是因为CameraX的分辨率选择机制和实际硬件支持之间的差异。让我为您分析一下原因和解决方案。

## 问题分析

1. **硬件限制**：相机硬件可能不支持您指定的确切分辨率1152x2016
2. **CameraX自动选择**：CameraX会从设备支持的分辨率中选择最接近的分辨率
3. **分辨率策略**：当前的回退策略可能选择了其他分辨率

## 解决方案

### 1. 添加分辨率检测和日志

首先，让我们添加代码来检测实际使用的分辨率：

```kotlin
// 在CameraManager.kt中添加分辨率检测
@ExperimentalGetImage
fun startCamera(
    previewView: PreviewView,
    lifecycleOwner: LifecycleOwner,
    enableRealtimeScanning: Boolean = false,
    onQRCodeDetected: ((List<String>) -> Unit)? = null,
    onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
) {
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

    cameraProviderFuture.addListener({
        cameraProvider = cameraProviderFuture.get()

        val analysisResolution = Size(1152, 2016)

        // 获取支持的分辨率列表进行调试
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
        val cameraInfo = cameraProvider?.getCameraInfo(cameraSelector)

        // 打印支持的分辨率（用于调试）
        try {
            val characteristics = Camera2CameraInfo.from(cameraInfo!!).getCameraCharacteristic(
                CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP
            )
            val supportedSizes = characteristics?.getOutputSizes(ImageFormat.YUV_420_888)
            Log.d("CameraManager", "支持的分辨率:")
            supportedSizes?.forEach { size ->
                Log.d("CameraManager", "  ${size.width}x${size.height}")
            }
        } catch (e: Exception) {
            Log.e("CameraManager", "无法获取支持的分辨率", e)
        }

        val resolutionSelector = ResolutionSelector.Builder()
            .setResolutionStrategy(
                ResolutionStrategy(
                    analysisResolution,
                    ResolutionStrategy.FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER
                )
            )
            .setResolutionFilter { supportedSizes, rotationDegrees ->
                Log.d("CameraManager", "ResolutionFilter输入分辨率:")
                supportedSizes.forEach { size ->
                    Log.d("CameraManager", "  ${size.width}x${size.height}")
                }

                // 寻找最接近目标分辨率的尺寸
                val targetSize = analysisResolution
                val sortedSizes = supportedSizes.sortedBy { size ->
                    val widthDiff = abs(size.width - targetSize.width)
                    val heightDiff = abs(size.height - targetSize.height)
                    widthDiff + heightDiff
                }

                Log.d("CameraManager", "选择的分辨率: ${sortedSizes.firstOrNull()}")
                sortedSizes
            }
            .build()

        // Preview配置
        val preview = Preview.Builder()
            .setResolutionSelector(resolutionSelector)
            .build()
            .also {
                it.surfaceProvider = previewView.surfaceProvider
            }

        val useCases = mutableListOf<UseCase>(preview)

        if (enableRealtimeScanning) {
            qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
            imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setResolutionSelector(resolutionSelector)
                .build()
                .also {
                    it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                }
            useCases.add(imageAnalyzer!!)
        }

        try {
            cameraProvider?.unbindAll()
            val camera = cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                CameraSelector.DEFAULT_BACK_CAMERA,
                *useCases.toTypedArray()
            )

            // 绑定后检查实际分辨率
            imageAnalyzer?.let { analyzer ->
                // 这里可以通过ResolutionInfo获取实际分辨率
                Log.d("CameraManager", "ImageAnalysis实际分辨率: ${analyzer.resolutionInfo}")
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }

    }, ContextCompat.getMainExecutor(context))
}
```

### 2. 在RealtimeQRAnalyzer中添加分辨率日志

```kotlin
// 在RealtimeQRAnalyzer.kt的analyze方法中添加
override fun analyze(imageProxy: ImageProxy) {
    val currentTimestamp = System.currentTimeMillis()

    // 控制扫描频率，避免过于频繁
    if (currentTimestamp - lastAnalyzedTimestamp < analyzeInterval) {
        imageProxy.close()
        return
    }

    lastAnalyzedTimestamp = currentTimestamp

    val mediaImage = imageProxy.image
    if (mediaImage != null) {
        // 打印实际图像分辨率
        Log.d("RealtimeQRAnalyzer", "实际图像分辨率: ${mediaImage.width}x${mediaImage.height}")
        Log.d("RealtimeQRAnalyzer", "ImageProxy分辨率: ${imageProxy.width}x${imageProxy.height}")

        val image = InputImage.fromMediaImage(
            mediaImage,
            imageProxy.imageInfo.rotationDegrees
        )

        // ... 其余代码保持不变
    }
}
```

### 3. 更精确的分辨率选择策略

如果您需要更精确的控制，可以使用这个改进的ResolutionFilter：

```kotlin
val resolutionSelector = ResolutionSelector.Builder()
    .setResolutionFilter { supportedSizes, rotationDegrees ->
        val targetSize = Size(1152, 2016)

        // 首先尝试找到完全匹配的分辨率
        val exactMatch = supportedSizes.find {
            it.width == targetSize.width && it.height == targetSize.height
        }

        if (exactMatch != null) {
            Log.d("CameraManager", "找到完全匹配的分辨率: ${exactMatch.width}x${exactMatch.height}")
            return@setResolutionFilter listOf(exactMatch)
        }

        // 如果没有完全匹配，寻找最接近的分辨率
        val closestMatch = supportedSizes.minByOrNull { size ->
            val aspectRatioDiff = abs(
                (size.width.toFloat() / size.height.toFloat()) -
                (targetSize.width.toFloat() / targetSize.height.toFloat())
            )
            val areaDiff = abs(size.width * size.height - targetSize.width * targetSize.height)

            // 综合考虑宽高比差异和面积差异
            aspectRatioDiff * 1000 + areaDiff / 1000
        }

        Log.d("CameraManager", "选择最接近的分辨率: ${closestMatch?.width}x${closestMatch?.height}")

        closestMatch?.let { listOf(it) } ?: supportedSizes
    }
    .build()
```

### 4. 检查设备支持的分辨率

您还可以添加一个辅助方法来检查设备支持的所有分辨率：

```kotlin
private fun logSupportedResolutions() {
    try {
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        val cameraId = cameraManager.cameraIdList[0] // 后置摄像头
        val characteristics = cameraManager.getCameraCharacteristics(cameraId)
        val map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)

        val outputSizes = map?.getOutputSizes(ImageFormat.YUV_420_888)
        Log.d("CameraManager", "设备支持的YUV_420_888分辨率:")
        outputSizes?.forEach { size ->
            Log.d("CameraManager", "  ${size.width}x${size.height} (${size.width.toFloat()/size.height.toFloat()})")
        }

        // 检查是否支持目标分辨率
        val targetSupported = outputSizes?.any { it.width == 1152 && it.height == 2016 }
        Log.d("CameraManager", "是否支持1152x2016: $targetSupported")

    } catch (e: Exception) {
        Log.e("CameraManager", "检查支持分辨率时出错", e)
    }
}
```

## 可能的原因

1. **设备不支持1152x2016**：您的设备可能不支持这个确切的分辨率
2. **宽高比限制**：相机可能只支持特定的宽高比
3. **硬件限制**：某些设备对分辨率有特殊限制

运行上述代码后，查看日志输出，您就能看到：
- 设备实际支持哪些分辨率
- CameraX最终选择了哪个分辨率
- 为什么没有选择您期望的分辨率

这样您就可以根据实际情况调整分辨率选择策略了。
