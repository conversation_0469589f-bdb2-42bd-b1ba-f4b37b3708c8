<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 曝光控制按钮样式 -->
    <style name="ExposureControlButton">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- 曝光控制文本样式 -->
    <style name="ExposureControlText">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textSize">12sp</item>
    </style>

    <!-- 曝光控制标题样式 -->
    <style name="ExposureControlTitle" parent="ExposureControlText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>