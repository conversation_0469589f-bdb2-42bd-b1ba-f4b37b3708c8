package com.yancao.qrscanner.camera

import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.CameraCharacteristics
import android.util.Log
import android.util.Size
import androidx.annotation.OptIn
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import androidx.camera.core.CameraControl
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.UseCase
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.common.Barcode
import java.util.concurrent.Executors
import kotlin.math.abs


/**
 * 相机管理器 - 负责管理CameraX相关功能
 *
 * 功能包括：
 * 1. 基础相机预览和图像分析
 * 2. 闪光灯控制
 * 3. 焦段缩放控制
 * 4. 实时二维码扫描
 */
@ExperimentalGetImage
class CameraManager(private val context: Context) {

    private var cameraProvider: ProcessCameraProvider? = null
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // 新增：图像分析器用于实时二维码扫描
    private var imageAnalyzer: ImageAnalysis? = null
    private var qrAnalyzer: RealtimeQRAnalyzer? = null

    private var cameraControl: CameraControl? = null
    private var cameraInfo: CameraInfo? = null

    @OptIn(ExperimentalCamera2Interop::class)
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        //二维码位置信息回调
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()

            //todo:可能需要改成自动获取组件分辨率
            val analysisResolution = Size(1152, 2016)

            val resolutionSelector = ResolutionSelector.Builder()
                .setResolutionStrategy(
                    ResolutionStrategy(
                        analysisResolution, // 首选分辨率
                        ResolutionStrategy.FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER // 回退策略
                    )
                ).setResolutionFilter { supportedSizes, rotationDegrees ->
                    val targetSize = Size(1152, 2016)

                    // 按照与目标分辨率的匹配度排序
                    val sortedSizes = supportedSizes.sortedBy { size ->
                        // 考虑旋转后的实际尺寸
                        val actualWidth = if (rotationDegrees == 90 || rotationDegrees == 270) {
                            size.height
                        } else {
                            size.width
                        }
                        val actualHeight = if (rotationDegrees == 90 || rotationDegrees == 270) {
                            size.width
                        } else {
                            size.height
                        }

                        // 计算与目标尺寸的差异
                        val widthDiff = abs(actualWidth - targetSize.width)
                        val heightDiff = abs(actualHeight - targetSize.height)
                        val totalDiff = widthDiff + heightDiff

                        Log.d("CameraManager", "分辨率 ${size.width}x${size.height} -> 旋转后 ${actualWidth}x${actualHeight}, 差异: $totalDiff")

                        totalDiff
                    }

                    // 返回排序后的前几个最佳选择
                    val bestChoices = sortedSizes.take(3)
                    Log.d("CameraManager", "选择的分辨率: ${bestChoices.map { "${it.width}x${it.height}" }}")

                    bestChoices.ifEmpty { supportedSizes }
                }
                .build()

            // Preview配置
            val preview = Preview.Builder()
                .setResolutionSelector(resolutionSelector)
                .build()
                .also {
                    it.surfaceProvider = previewView.surfaceProvider
                }

            // 新增：如果启用实时扫描，创建图像分析器
            val useCases = mutableListOf<UseCase>(preview)

            if (enableRealtimeScanning) {
                qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
                imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setResolutionSelector(resolutionSelector) // 使用相同的分辨率选择器
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                    }
                useCases.add(imageAnalyzer!!)
            }

            try {
                cameraProvider?.unbindAll()
                val camera = cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    CameraSelector.DEFAULT_BACK_CAMERA,
                    *useCases.toTypedArray()
                )

                // 获取相机控制对象
                camera?.let {
                    cameraControl = it.cameraControl
                    cameraInfo = it.cameraInfo
                }

                // 绑定后检查实际分辨率
                imageAnalyzer?.let { analyzer ->
                    // 这里可以通过ResolutionInfo获取实际分辨率
                    Log.d("CameraManager", "ImageAnalysis实际分辨率: ${analyzer.resolutionInfo}")
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }

        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * 设置闪光灯状态
     * @param enabled true为开启，false为关闭
     * @return 操作是否成功
     */
    fun setFlashlight(enabled: Boolean): Boolean {
        return try {
            cameraControl?.enableTorch(enabled)
            Log.d("CameraManager", "闪光灯${if (enabled) "开启" else "关闭"}成功")
            true
        } catch (e: Exception) {
            Log.e("CameraManager", "闪光灯控制失败", e)
            false
        }
    }

    /**
     * 检查是否支持闪光灯
     * @return true表示支持闪光灯
     */
    fun hasFlashlight(): Boolean {
        return cameraInfo?.hasFlashUnit() ?: false
    }

    /**
     * 设置缩放比例
     * @param zoomRatio 缩放比例，1.0为原始大小
     * @return 操作是否成功
     */
    fun setZoomRatio(zoomRatio: Float): Boolean {
        return try {
            val clampedRatio = zoomRatio.coerceIn(getMinZoomRatio(), getMaxZoomRatio())
            cameraControl?.setZoomRatio(clampedRatio)
            Log.d("CameraManager", "设置缩放比例: $clampedRatio")
            true
        } catch (e: Exception) {
            Log.e("CameraManager", "缩放控制失败", e)
            false
        }
    }

    /**
     * 获取最小缩放比例
     * @return 最小缩放比例
     */
    fun getMinZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.minZoomRatio ?: 1.0f
    }

    /**
     * 获取最大缩放比例
     * @return 最大缩放比例
     */
    fun getMaxZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.maxZoomRatio ?: 1.0f
    }

    /**
     * 获取当前缩放比例
     * @return 当前缩放比例
     */
    fun getCurrentZoomRatio(): Float {
        return cameraInfo?.zoomState?.value?.zoomRatio ?: 1.0f
    }


    /**
     * 设置曝光补偿
     * @param exposureIndex 曝光补偿索引值
     * @return 操作是否成功
     */
    fun setExposureCompensation(exposureIndex: Int): Boolean {
        return try {
            val clampedIndex = exposureIndex.coerceIn(getMinExposureCompensation(), getMaxExposureCompensation())
            cameraControl?.setExposureCompensationIndex(clampedIndex)
            Log.d("CameraManager", "设置曝光补偿: $clampedIndex")
            true
        } catch (e: Exception) {
            Log.e("CameraManager", "曝光补偿控制失败", e)
            false
        }
    }

    /**
     * 获取最小曝光补偿值
     * @return 最小曝光补偿索引
     */
    fun getMinExposureCompensation(): Int {
        return cameraInfo?.exposureState?.exposureCompensationRange?.lower ?: -2
    }

    /**
     * 获取最大曝光补偿值
     * @return 最大曝光补偿索引
     */
    fun getMaxExposureCompensation(): Int {
        return cameraInfo?.exposureState?.exposureCompensationRange?.upper ?: 2
    }

    /**
     * 获取当前曝光补偿值
     * @return 当前曝光补偿索引
     */
    fun getCurrentExposureCompensation(): Int {
        return cameraInfo?.exposureState?.exposureCompensationIndex ?: 0
    }

    /**
     * 获取曝光补偿步长
     * @return 曝光补偿步长值
     */
    fun getExposureCompensationStep(): Float {
        return cameraInfo?.exposureState?.exposureCompensationStep?.toFloat() ?: 0.5f
    }


    fun shutdown() {
        cameraExecutor.shutdown()
    }
}