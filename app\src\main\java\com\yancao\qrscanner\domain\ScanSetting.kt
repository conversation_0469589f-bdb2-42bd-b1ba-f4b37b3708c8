package com.yancao.qrscanner.domain

/**
 * 扫描设置数据类
 */
data class ScanSettings(
    val scanFrequencyValue: Int = 1,
    val scanFrequencyUnit: FrequencyUnit = FrequencyUnit.FRAMES,
    val drawFrequencyValue: Int = 1,
    val drawFrequencyUnit: FrequencyUnit = FrequencyUnit.FRAMES
)

/**
 * 频率单位枚举
 */
enum class FrequencyUnit(val displayName: String, val description: String) {
    MILLISECONDS("毫秒", "时间间隔"),
    FRAMES("帧数", "帧数间隔")
}

/**
 * 扫描设置管理器
 */
object ScanSettingsManager {
    private var currentSettings = ScanSettings()

    fun getCurrentSettings(): ScanSettings = currentSettings

    fun updateSettings(newSettings: ScanSettings) {
        currentSettings = newSettings
    }

    /**
     * 将扫描频率转换为毫秒间隔
     */
    fun getScanIntervalMs(): Long {
        return when (currentSettings.scanFrequencyUnit) {
            FrequencyUnit.MILLISECONDS -> currentSettings.scanFrequencyValue.toLong()
            FrequencyUnit.FRAMES -> {
                // 假设30fps，将帧数转换为毫秒
                (currentSettings.scanFrequencyValue * 1000L / 30).coerceAtLeast(100L)
            }
        }
    }

    /**
     * 获取绘制频率（帧数）
     */
    fun getDrawFrequency(): Int {
        return when (currentSettings.drawFrequencyUnit) {
            FrequencyUnit.FRAMES -> currentSettings.drawFrequencyValue
            FrequencyUnit.MILLISECONDS -> {
                // 假设30fps，将毫秒转换为帧数
                (currentSettings.drawFrequencyValue * 30 / 1000).coerceAtLeast(1)
            }
        }
    }
}