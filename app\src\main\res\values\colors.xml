<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="exposure_dark">#FF6B6B</color>      <!-- 曝光不足时的红色 -->
    <color name="exposure_normal">#4ECDC4</color>    <!-- 正常曝光时的青色 -->
    <color name="exposure_bright">#FFE66D</color>    <!-- 曝光过度时的黄色 -->

    <!-- 新增曝光控制相关颜色 -->
    <color name="semi_transparent_black">#80000000</color>
    <color name="button_primary">#2196F3</color>      <!-- 蓝色主按钮 -->
    <color name="button_secondary">#FF9800</color>    <!-- 橙色次要按钮 -->
    <color name="button_accent">#4CAF50</color>       <!-- 绿色强调按钮 -->
    <color name="button_tertiary">#9C27B0</color>     <!-- 紫色第三按钮 -->
    <color name="exposure_control_bg">#E0000000</color> <!-- 曝光控制背景 -->
    <color name="text_primary_light">#FFFFFF</color>   <!-- 主要文字颜色（浅色） -->
    <color name="text_secondary_light">#B3FFFFFF</color> <!-- 次要文字颜色（浅色） -->
</resources>